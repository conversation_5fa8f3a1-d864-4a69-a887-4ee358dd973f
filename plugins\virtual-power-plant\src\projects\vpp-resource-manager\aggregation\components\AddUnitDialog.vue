<template>
  <el-dialog
    title="新增"
    :visible.sync="dialogVisible"
    width="960px"
    :before-close="handleClose"
    class="add-unit-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <div class="form-row">
          <div class="form-item">
            <label class="form-label required">{{ $T('机组名称') }} *</label>
            <el-input
              v-model="formData.unitName"
              :placeholder="$T('请输入内容')"
              class="form-input"
            />
          </div>
          <div class="form-item">
            <label class="form-label required">{{ $T('机组类型') }} *</label>
            <el-select
              v-model="formData.unitType"
              class="form-select"
              :placeholder="$T('请选择机组类型')"
            >
              <el-option
                v-for="option in unitTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </div>

      <!-- 资源列表区域 -->
      <div class="resource-section">
        <div class="section-title">{{ $T('调峰机组资源列表') }}</div>
        
        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <div class="filter-left">
            <el-input
              v-model="resourceSearch"
              :placeholder="$T('请输入关键字')"
              prefix-icon="el-icon-search"
              class="search-input"
            />
            <el-select
              v-model="selectedArea"
              class="area-select"
              :placeholder="$T('区域')"
            >
              <el-option
                v-for="option in areaOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <el-button type="primary" class="bind-btn">
            {{ $T('绑定资源') }}
          </el-button>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <el-table
            :data="currentResourceData"
            @selection-change="handleSelectionChange"
            max-height="300"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
            />
            <el-table-column
              prop="index"
              :label="$T('序号')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                {{ String(scope.row.index).padStart(2, '0') }}
              </template>
            </el-table-column>
            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="180"
            />
            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="150"
            />
            <el-table-column
              prop="area"
              :label="$T('区域')"
              min-width="100"
            />
            <el-table-column
              prop="status"
              :label="$T('状态')"
              min-width="100"
            />
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="resource-pagination">
          <el-pagination
            :current-page="resourceCurrentPage"
            :page-size="resourcePageSize"
            :total="resourceTotalCount"
            layout="prev, pager, next, jumper"
            @current-change="handleResourcePageChange"
            class="pagination-component"
          />
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">{{ $T('取消') }}</el-button>
      <el-button type="primary" @click="handleConfirm">{{ $T('确定') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddUnitDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      
      // 表单数据
      formData: {
        unitName: '',
        unitType: 'peak'
      },
      
      // 机组类型选项
      unitTypeOptions: [
        { label: this.$T('调峰机组'), value: 'peak' },
        { label: this.$T('基荷机组'), value: 'base' },
        { label: this.$T('调频机组'), value: 'frequency' },
        { label: this.$T('备用机组'), value: 'backup' }
      ],
      
      // 资源搜索和筛选
      resourceSearch: '',
      selectedArea: 'all',
      areaOptions: [
        { label: this.$T('全部'), value: 'all' },
        { label: this.$T('华北区域'), value: 'north' },
        { label: this.$T('华东区域'), value: 'east' },
        { label: this.$T('华南区域'), value: 'south' },
        { label: this.$T('西北区域'), value: 'northwest' }
      ],
      
      // 资源列表数据
      allResourceData: [],
      selectedResources: [],
      
      // 分页
      resourceCurrentPage: 1,
      resourcePageSize: 10
    };
  },
  computed: {
    currentResourceData() {
      const start = (this.resourceCurrentPage - 1) * this.resourcePageSize;
      const end = start + this.resourcePageSize;
      return this.allResourceData.slice(start, end);
    },
    
    resourceTotalCount() {
      return this.allResourceData.length;
    }
  },
  watch: {
    visible: {
      handler(val) {
        console.log('AddUnitDialog visible prop changed:', val);
        this.dialogVisible = val;
        if (val) {
          this.initDialog();
        }
      },
      immediate: true
    },

    dialogVisible(val) {
      console.log('AddUnitDialog dialogVisible changed:', val);
      this.$emit('update:visible', val);
    }
  },
  created() {
    console.log('AddUnitDialog created');
    this.generateResourceMockData();
  },
  mounted() {
    console.log('AddUnitDialog mounted, visible prop:', this.visible);
  },
  methods: {
    // 初始化弹窗
    initDialog() {
      this.formData = {
        unitName: '',
        unitType: 'peak'
      };
      this.resourceSearch = '';
      this.selectedArea = 'all';
      this.selectedResources = [];
      this.resourceCurrentPage = 1;
    },
    
    // 生成资源Mock数据
    generateResourceMockData() {
      const data = [];
      const areas = [this.$T('华北区域'), this.$T('华东区域'), this.$T('华南区域'), this.$T('西北区域')];
      const statuses = [this.$T('在线'), this.$T('离线'), this.$T('维护中')];
      
      for (let i = 1; i <= 90; i++) {
        data.push({
          index: i,
          resourceId: `9144030078525478X${i}`,
          resourceName: `${this.$T('资源')}${i}`,
          area: areas[Math.floor(Math.random() * areas.length)],
          status: statuses[Math.floor(Math.random() * statuses.length)]
        });
      }
      this.allResourceData = data;
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedResources = selection;
    },
    
    // 资源分页变化
    handleResourcePageChange(page) {
      this.resourceCurrentPage = page;
    },
    
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
    },
    
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    
    // 确定
    handleConfirm() {
      // 表单验证
      if (!this.formData.unitName.trim()) {
        this.$message.warning(this.$T('请输入机组名称'));
        return;
      }
      
      if (!this.formData.unitType) {
        this.$message.warning(this.$T('请选择机组类型'));
        return;
      }
      
      // 提交数据
      const submitData = {
        ...this.formData,
        selectedResources: this.selectedResources
      };
      
      this.$emit('confirm', submitData);
      this.dialogVisible = false;
      this.$message.success(this.$T('新增成功'));
    }
  }
};
</script>

<style lang="scss" scoped>
.add-unit-dialog {
  .dialog-content {
    @include padding(J4);
    
    .form-section {
      @include margin_bottom(J4);
      
      .form-row {
        display: flex;
        gap: var(--J4);
        
        .form-item {
          flex: 1;
          
          .form-label {
            display: block;
            @include font_size(Aa);
            @include font_color(T1);
            @include margin_bottom(J1);
            
            &.required {
              @include font_color(T1);
            }
          }
          
          .form-input,
          .form-select {
            width: 100%;
          }
        }
      }
    }
    
    .resource-section {
      .section-title {
        @include font_size(Aa);
        @include font_color(T1);
        @include margin_bottom(J2);
      }
      
      .resource-filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include margin_bottom(J3);
        
        .filter-left {
          display: flex;
          gap: var(--J3);
          
          .search-input {
            width: 240px;
          }
          
          .area-select {
            width: 240px;
          }
        }
        
        .bind-btn {
          opacity: 0;
        }
      }
      
      .resource-table {
        @include margin_bottom(J3);
      }
      
      .resource-pagination {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--J2);
  }
}
</style>
